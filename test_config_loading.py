#!/usr/bin/env python3
"""
Test script to verify configuration loading works correctly.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.fine_tune import FineTuningPipeline

def test_config_loading():
    """Test that configuration values have correct types."""
    
    print("🧪 Testing Configuration Loading")
    print("=" * 50)
    
    configs_to_test = [
        "configs/instruction_tuning.yaml",
        "configs/continued_pretraining.yaml"
    ]
    
    for config_path in configs_to_test:
        print(f"\n📋 Testing: {config_path}")
        
        try:
            pipeline = FineTuningPipeline(config_path=config_path)
            config = pipeline.config
            
            # Check training parameters
            lr = config["training"]["learning_rate"]
            print(f"  learning_rate: {lr} (type: {type(lr).__name__})")
            
            weight_decay = config["training"]["weight_decay"]
            print(f"  weight_decay: {weight_decay} (type: {type(weight_decay).__name__})")
            
            epochs = config["training"]["num_train_epochs"]
            print(f"  num_train_epochs: {epochs} (type: {type(epochs).__name__})")
            
            batch_size = config["training"]["per_device_train_batch_size"]
            print(f"  per_device_train_batch_size: {batch_size} (type: {type(batch_size).__name__})")
            
            # Check LoRA parameters
            lora_r = config["lora"]["r"]
            print(f"  lora_r: {lora_r} (type: {type(lora_r).__name__})")
            
            lora_dropout = config["lora"]["dropout"]
            print(f"  lora_dropout: {lora_dropout} (type: {type(lora_dropout).__name__})")
            
            # Verify types
            assert isinstance(lr, float), f"learning_rate should be float, got {type(lr)}"
            assert isinstance(weight_decay, float), f"weight_decay should be float, got {type(weight_decay)}"
            assert isinstance(epochs, int), f"num_train_epochs should be int, got {type(epochs)}"
            assert isinstance(batch_size, int), f"per_device_train_batch_size should be int, got {type(batch_size)}"
            assert isinstance(lora_r, int), f"lora_r should be int, got {type(lora_r)}"
            assert isinstance(lora_dropout, float), f"lora_dropout should be float, got {type(lora_dropout)}"
            
            print("  ✅ All types are correct!")
            
        except Exception as e:
            print(f"  ❌ Error: {e}")
            return False
    
    print(f"\n{'='*50}")
    print("✅ Configuration loading test passed!")
    print("The learning rate type issue should be fixed.")
    return True

if __name__ == "__main__":
    test_config_loading()
