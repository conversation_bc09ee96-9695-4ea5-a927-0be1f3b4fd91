# Training Data Inspection Tools

This directory contains tools to inspect and visualize the training data before it goes into the model. These tools help you understand the difference between instruction tuning and continued pretraining.

## 🔍 Available Tools

### 1. Jupyter Notebook: `inspect_training_data.ipynb`
**Interactive analysis with rich visualizations**

Features:
- 📊 Interactive tables with color-coded token analysis
- 🎨 HTML visualizations of masking patterns
- 📈 Statistical comparisons across samples
- 🔍 Detailed token-by-token breakdown

**Usage:**
```bash
jupyter notebook inspect_training_data.ipynb
```

### 2. Python Script: `inspect_training_data.py`
**Command-line analysis with colored output**

Features:
- 🌈 Color-coded terminal output
- 📋 Side-by-side comparison of training modes
- 📊 Summary statistics
- 🚀 No dependencies on Jupyter

**Usage:**
```bash
python inspect_training_data.py
```

## 📋 What These Tools Show

### Input Data Analysis
- **Raw conversation format** (user/assistant messages)
- **Chat template output** (how messages are formatted)
- **Tokenized input** (actual token IDs that go into the model)
- **Label masking** (which tokens contribute to loss calculation)

### Training Mode Comparison

#### 🎯 Instruction Tuning (Recommended for Chat Models)
- ✅ **Masks system/user prompts** with -100
- ✅ **Only trains on assistant responses**
- ✅ **Focuses learning** on response generation
- ⚠️ **Lower token utilization** (~30-50% of tokens)

#### 🔄 Continued Pretraining (For Domain Knowledge)
- ✅ **Trains on all tokens** (100% utilization)
- ✅ **Higher token efficiency**
- ⚠️ **May learn to predict user inputs**
- ⚠️ **Less focused on response quality**

## 📊 Sample Output

```
🎯 INSTRUCTION TUNING MODE (Masking Enabled)
==================================================
Input tokens: 2048
Masked tokens: 79
Trained tokens: 1969
Training ratio: 96.1%

🔄 CONTINUED PRETRAINING MODE (No Masking)
==================================================
Input tokens: 2048
Masked tokens: 0
Trained tokens: 2048
Training ratio: 100.0%
```

### Token-by-Token Analysis
```
Pos  Token ID Token Text           Instruction  Pretraining 
------------------------------------------------------------------------
0    151644   '<|im_start|>'       MASK         TRAIN  
1    8948     'system'             MASK         TRAIN  
2    198      '\n'                 MASK         TRAIN  
...
25   151644   '<|im_start|>'       TRAIN        TRAIN  
26   78191    'assistant'          TRAIN        TRAIN  
```

## 🎨 Visualization Features

### Color Coding
- 🔴 **Red tokens**: Masked (don't contribute to loss)
- 🟢 **Green tokens**: Trained (contribute to loss)
- 🔵 **Blue**: User messages
- 🟡 **Yellow**: System messages

### Statistics
- **Total tokens**: Number of input tokens
- **Masked tokens**: Tokens with labels = -100
- **Trained tokens**: Tokens that contribute to loss
- **Training ratio**: Percentage of tokens used for training

## 🚀 Quick Start

1. **Run the Python script** for a quick overview:
   ```bash
   python inspect_training_data.py
   ```

2. **Open the Jupyter notebook** for detailed analysis:
   ```bash
   jupyter notebook inspect_training_data.ipynb
   ```

3. **Modify the data file** to inspect your own data:
   ```python
   # In the script/notebook, change this line:
   data_file = "path/to/your/data.jsonl"
   ```

## 📁 File Structure

```
├── inspect_training_data.ipynb    # Jupyter notebook (interactive)
├── inspect_training_data.py       # Python script (command-line)
├── configs/
│   ├── instruction_tuning.yaml    # Config with masking enabled
│   └── continued_pretraining.yaml # Config with masking disabled
└── data/
    └── code_switched_GAIR_LIMO_train_817.jsonl  # Sample data
```

## 🔧 Requirements

- Python 3.7+
- transformers
- torch
- termcolor (for colored output)
- pandas (for Jupyter notebook)
- jupyter (for notebook interface)

Install with:
```bash
pip install transformers torch termcolor pandas jupyter
```

## 💡 Use Cases

### Before Training
- **Verify masking is working correctly**
- **Check training ratios** across samples
- **Understand token distribution**
- **Debug data formatting issues**

### Model Comparison
- **Compare different masking strategies**
- **Analyze token efficiency**
- **Understand training focus**
- **Validate data preprocessing**

### Debugging
- **Check chat template formatting**
- **Verify special token handling**
- **Inspect tokenization issues**
- **Validate label alignment**

## 📈 Interpreting Results

### Good Instruction Tuning Signs
- ✅ System/user tokens are masked (red)
- ✅ Assistant tokens are trained (green)
- ✅ Training ratio ~30-70% (depending on conversation length)
- ✅ Clear separation between masked and trained regions

### Warning Signs
- ⚠️ All tokens are trained (100% ratio) when expecting masking
- ⚠️ No tokens are masked when expecting instruction tuning
- ⚠️ Inconsistent masking patterns across samples
- ⚠️ Very low training ratios (<20%) may indicate over-masking

## 🎯 Next Steps

After inspecting your data:

1. **Adjust configuration** if needed
2. **Run training** with verified settings
3. **Monitor training metrics** in WandB
4. **Compare model performance** between approaches
5. **Iterate on data formatting** if necessary
