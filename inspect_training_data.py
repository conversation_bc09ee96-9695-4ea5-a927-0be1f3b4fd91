#!/usr/bin/env python3
"""
Training Data Inspection Script

This script shows exactly what data goes into the model during training, including:
- Raw input tokens
- Decoded text  
- Label masking for instruction tuning
- Comparison between instruction tuning and continued pretraining
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.fine_tune import FineTuningPipeline
from transformers import AutoTokenizer
import json
from termcolor import colored

def load_sample_data(data_file="data/code_switched_GAIR_LIMO_train_817.jsonl", num_samples=3):
    """Load sample data from JSONL file."""
    samples = []
    with open(data_file, 'r', encoding='utf-8') as f:
        for i, line in enumerate(f):
            if i >= num_samples:
                break
            samples.append(json.loads(line.strip()))
    return samples

def setup_pipelines_and_tokenizer():
    """Setup pipelines and tokenizer."""
    # Initialize pipelines
    instruction_pipeline = FineTuningPipeline(config_path="configs/instruction_tuning.yaml")
    pretraining_pipeline = FineTuningPipeline(config_path="configs/continued_pretraining.yaml")
    
    # Load tokenizer
    model_name = instruction_pipeline.config["model"]["name"]
    tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)
    
    # Add special tokens
    special_tokens = ["<think>", "</think>"]
    tokenizer.add_tokens(special_tokens)
    
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    return instruction_pipeline, pretraining_pipeline, tokenizer

def inspect_sample(sample, instruction_pipeline, tokenizer, sample_idx=0):
    """Inspect a specific sample with both training modes."""
    
    print(f"\n{'='*80}")
    print(f"SAMPLE {sample_idx + 1} INSPECTION")
    print(f"{'='*80}")
    
    messages = sample["messages"]
    
    # Show original conversation
    print("\n📋 ORIGINAL CONVERSATION:")
    print("-" * 40)
    for msg in messages:
        role_color = "blue" if msg["role"] == "user" else "green" if msg["role"] == "assistant" else "yellow"
        content_preview = msg["content"][:200] + ("..." if len(msg["content"]) > 200 else "")
        print(colored(f"{msg['role'].upper()}:", role_color, attrs=['bold']))
        print(f"{content_preview}\n")
    
    # Apply chat template
    full_text = tokenizer.apply_chat_template(
        messages, tokenize=False, add_generation_prompt=False
    )
    
    print("\n💬 CHAT TEMPLATE OUTPUT:")
    print("-" * 40)
    print(f"Length: {len(full_text)} characters")
    print(f"Text: {full_text[:300]}{'...' if len(full_text) > 300 else ''}")
    
    # Instruction tuning mode
    print(f"\n\n{colored('🎯 INSTRUCTION TUNING MODE (Masking Enabled)', 'green', attrs=['bold'])}")
    print("=" * 50)
    
    input_ids_inst, labels_inst = instruction_pipeline._tokenize_with_masking(messages, tokenizer)
    
    masked_count = sum(1 for l in labels_inst if l == -100)
    trained_count = len(labels_inst) - masked_count
    training_ratio = trained_count / len(labels_inst) * 100
    
    print(f"Input tokens: {len(input_ids_inst)}")
    print(colored(f"Masked tokens: {masked_count}", 'red'))
    print(colored(f"Trained tokens: {trained_count}", 'green'))
    print(f"Training ratio: {training_ratio:.1f}%")
    
    # Continued pretraining mode
    print(f"\n\n{colored('🔄 CONTINUED PRETRAINING MODE (No Masking)', 'yellow', attrs=['bold'])}")
    print("=" * 50)
    
    tokenized_pretrain = tokenizer(
        full_text,
        truncation=True,
        padding=False,
        max_length=2048,
        return_tensors=None,
    )
    input_ids_pretrain = tokenized_pretrain["input_ids"]
    labels_pretrain = tokenized_pretrain["input_ids"].copy()
    
    print(f"Input tokens: {len(input_ids_pretrain)}")
    print("Masked tokens: 0")
    print(colored(f"Trained tokens: {len(labels_pretrain)}", 'green'))
    print("Training ratio: 100.0%")
    
    return {
        'messages': messages,
        'full_text': full_text,
        'instruction_tuning': {
            'input_ids': input_ids_inst,
            'labels': labels_inst
        },
        'continued_pretraining': {
            'input_ids': input_ids_pretrain,
            'labels': labels_pretrain
        }
    }

def show_token_analysis(sample_data, tokenizer, max_tokens=30):
    """Show detailed token-by-token analysis."""
    
    inst_data = sample_data['instruction_tuning']
    
    print(f"\n🔍 TOKEN-BY-TOKEN ANALYSIS (First {max_tokens} tokens)")
    print("=" * 80)
    print(f"{'Pos':<4} {'Token ID':<8} {'Token Text':<20} {'Instruction':<12} {'Pretraining':<12}")
    print("-" * 80)
    
    for i in range(min(max_tokens, len(inst_data['input_ids']))):
        token_id = inst_data['input_ids'][i]
        token_text = repr(tokenizer.decode([token_id]))
        
        # Instruction tuning status
        inst_label = inst_data['labels'][i]
        inst_status = "TRAIN" if inst_label != -100 else "MASK"
        
        # Color coding
        if inst_status == "MASK":
            inst_colored = colored("MASK", 'red', attrs=['bold'])
        else:
            inst_colored = colored("TRAIN", 'green', attrs=['bold'])
        
        pretrain_colored = colored("TRAIN", 'green', attrs=['bold'])
        
        print(f"{i:<4} {token_id:<8} {token_text:<20} {inst_colored:<20} {pretrain_colored:<20}")

def visualize_masking_pattern(sample_data, tokenizer, max_tokens=50):
    """Create a text-based visualization of the masking pattern."""
    
    inst_data = sample_data['instruction_tuning']
    
    print(f"\n🎯 INSTRUCTION TUNING: Token Masking Visualization")
    print("=" * 60)
    print("Legend: " + colored("MASKED", 'red', 'on_white') + " " + colored("TRAINED", 'green', 'on_white'))
    print("-" * 60)
    
    line_length = 0
    max_line_length = 80
    
    for i in range(min(max_tokens, len(inst_data['input_ids']))):
        token_id = inst_data['input_ids'][i]
        token_text = tokenizer.decode([token_id])
        label = inst_data['labels'][i]
        
        # Color based on masking
        if label == -100:  # Masked
            colored_token = colored(token_text, 'red', 'on_white', attrs=['bold'])
        else:  # Trained
            colored_token = colored(token_text, 'green', 'on_white', attrs=['bold'])
        
        # Line wrapping
        if line_length + len(token_text) > max_line_length:
            print()
            line_length = 0
        
        print(colored_token, end='')
        line_length += len(token_text)
    
    if len(inst_data['input_ids']) > max_tokens:
        print(colored(f"\n... ({len(inst_data['input_ids']) - max_tokens} more tokens)", 'cyan'))
    
    print("\n")

def compare_samples(formatted_samples, instruction_pipeline, tokenizer):
    """Compare masking statistics across all samples."""
    
    print(f"\n📊 COMPARISON ACROSS ALL SAMPLES")
    print("=" * 80)
    print(f"{'Sample':<8} {'Total':<8} {'Masked':<8} {'Trained':<8} {'Ratio':<8} {'Question Preview':<30}")
    print("-" * 80)
    
    total_ratios = []
    
    for i, sample in enumerate(formatted_samples):
        messages = sample["messages"]
        
        # Get instruction tuning data
        input_ids, labels = instruction_pipeline._tokenize_with_masking(messages, tokenizer)
        
        # Calculate statistics
        total_tokens = len(input_ids)
        masked_tokens = sum(1 for l in labels if l == -100)
        trained_tokens = total_tokens - masked_tokens
        training_ratio = trained_tokens / total_tokens * 100
        total_ratios.append(training_ratio)
        
        # Get question preview
        user_content = next((msg['content'] for msg in messages if msg['role'] == 'user'), "")
        question_preview = (user_content[:30] + "...") if len(user_content) > 30 else user_content
        
        print(f"{i+1:<8} {total_tokens:<8} {masked_tokens:<8} {trained_tokens:<8} {training_ratio:.1f}%{'':<3} {question_preview:<30}")
    
    print("-" * 80)
    avg_ratio = sum(total_ratios) / len(total_ratios)
    print(f"Average training ratio: {avg_ratio:.1f}%")

def main():
    """Main function to run the inspection."""
    
    print("🔍 TRAINING DATA INSPECTION")
    print("=" * 80)
    print("This script shows exactly what goes into the model during training.")
    print("It compares instruction tuning (with masking) vs continued pretraining (no masking).")
    
    try:
        # Load data and setup
        print("\n📁 Loading sample data...")
        samples = load_sample_data()
        print(f"Loaded {len(samples)} samples")
        
        print("\n⚙️ Setting up pipelines and tokenizer...")
        instruction_pipeline, pretraining_pipeline, tokenizer = setup_pipelines_and_tokenizer()
        print(f"Tokenizer loaded: {len(tokenizer)} tokens")
        
        # Format samples
        print("\n📝 Formatting training data...")
        formatted_samples = instruction_pipeline.format_training_data(samples)
        print(f"Formatted {len(formatted_samples)} samples")
        
        # Inspect first sample in detail
        sample_data = inspect_sample(formatted_samples[0], instruction_pipeline, tokenizer, 0)
        
        # Show token analysis
        show_token_analysis(sample_data, tokenizer, max_tokens=25)
        
        # Visualize masking pattern
        visualize_masking_pattern(sample_data, tokenizer, max_tokens=40)
        
        # Compare all samples
        compare_samples(formatted_samples, instruction_pipeline, tokenizer)
        
        print(f"\n💡 KEY INSIGHTS:")
        print("=" * 40)
        print("🎯 Instruction Tuning:")
        print("  - Masks system/user prompts with -100")
        print("  - Only trains on assistant responses")
        print("  - ~30-50% token utilization")
        print("  - Better for chat and instruction following")
        print()
        print("🔄 Continued Pretraining:")
        print("  - Trains on all tokens")
        print("  - 100% token utilization")
        print("  - Better for domain knowledge injection")
        print("  - May learn to predict user inputs")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
