import sys
import os
sys.path.append(os.path.dirname(os.path.abspath('.')))

from src.fine_tune import FineTuningPipeline
from transformers import AutoTokenizer
import json
import torch
import pandas as pd
from IPython.display import display, HTML
import numpy as np

# Load a few samples from the training data
data_file = "data/code_switched_GAIR_LIMO_train_817.jsonl"

samples = []
with open(data_file, 'r', encoding='utf-8') as f:
    for i, line in enumerate(f):
        if i >= 3:  # Just load first 3 samples
            break
        samples.append(json.loads(line.strip()))

print(f"Loaded {len(samples)} samples")
print("\nFirst sample structure:")
print(json.dumps(samples[0], indent=2, ensure_ascii=False)[:500] + "...")

# Initialize instruction tuning pipeline
instruction_pipeline = FineTuningPipeline(config_path="configs/instruction_tuning.yaml")

# Initialize continued pretraining pipeline
pretraining_pipeline = FineTuningPipeline(config_path="configs/continued_pretraining.yaml")

print("Instruction tuning enabled:", instruction_pipeline.config["dataset"].get("instruction_tuning", True))
print("Pretraining masking disabled:", not pretraining_pipeline.config["dataset"].get("instruction_tuning", True))

# Load tokenizer
model_name = instruction_pipeline.config["model"]["name"]
tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)

# Add special tokens
special_tokens = ["<think>", "</think>"]
tokenizer.add_tokens(special_tokens)

if tokenizer.pad_token is None:
    tokenizer.pad_token = tokenizer.eos_token

print(f"Tokenizer loaded: {len(tokenizer)} tokens")
print(f"Special tokens added: {special_tokens}")

# Format the sample data
formatted_samples = instruction_pipeline.format_training_data(samples)

print(f"Formatted {len(formatted_samples)} samples")
print("\nFormatted sample structure:")
sample = formatted_samples[0]
print("Messages:")
for msg in sample["messages"]:
    print(f"  {msg['role'].upper()}: {msg['content'][:100]}...")

def inspect_sample(sample_idx=0):
    """Inspect a specific sample with both training modes."""
    
    sample = formatted_samples[sample_idx]
    messages = sample["messages"]
    
    print(f"\n{'='*80}")
    print(f"SAMPLE {sample_idx + 1} INSPECTION")
    print(f"{'='*80}")
    
    # Show original conversation
    print("\n📋 ORIGINAL CONVERSATION:")
    print("-" * 40)
    for msg in messages:
        print(f"{msg['role'].upper()}: {msg['content'][:200]}{'...' if len(msg['content']) > 200 else ''}")
        print()
    
    # Apply chat template
    full_text = tokenizer.apply_chat_template(
        messages, tokenize=False, add_generation_prompt=False
    )
    
    print("\n💬 CHAT TEMPLATE OUTPUT:")
    print("-" * 40)
    print(f"Length: {len(full_text)} characters")
    print(f"Text: {full_text[:300]}{'...' if len(full_text) > 300 else ''}")
    
    # Instruction tuning mode
    print("\n\n🎯 INSTRUCTION TUNING MODE (Masking Enabled)")
    print("=" * 50)
    
    input_ids_inst, labels_inst = instruction_pipeline._tokenize_with_masking(messages, tokenizer)
    
    print(f"Input tokens: {len(input_ids_inst)}")
    print(f"Masked tokens: {sum(1 for l in labels_inst if l == -100)}")
    print(f"Trained tokens: {sum(1 for l in labels_inst if l != -100)}")
    print(f"Training ratio: {sum(1 for l in labels_inst if l != -100) / len(labels_inst) * 100:.1f}%")
    
    # Continued pretraining mode
    print("\n\n🔄 CONTINUED PRETRAINING MODE (No Masking)")
    print("=" * 50)
    
    tokenized_pretrain = tokenizer(
        full_text,
        truncation=True,
        padding=False,
        max_length=2048,
        return_tensors=None,
    )
    input_ids_pretrain = tokenized_pretrain["input_ids"]
    labels_pretrain = tokenized_pretrain["input_ids"].copy()
    
    print(f"Input tokens: {len(input_ids_pretrain)}")
    print(f"Masked tokens: 0")
    print(f"Trained tokens: {len(labels_pretrain)}")
    print(f"Training ratio: 100.0%")
    
    return {
        'messages': messages,
        'full_text': full_text,
        'instruction_tuning': {
            'input_ids': input_ids_inst,
            'labels': labels_inst
        },
        'continued_pretraining': {
            'input_ids': input_ids_pretrain,
            'labels': labels_pretrain
        }
    }

# Inspect first sample
sample_data = inspect_sample(0)

def show_token_analysis(sample_data, max_tokens=50):
    """Show detailed token-by-token analysis."""
    
    inst_data = sample_data['instruction_tuning']
    pretrain_data = sample_data['continued_pretraining']
    
    print(f"\n🔍 TOKEN-BY-TOKEN ANALYSIS (First {max_tokens} tokens)")
    print("=" * 80)
    
    # Create comparison table
    rows = []
    
    for i in range(min(max_tokens, len(inst_data['input_ids']))):
        token_id = inst_data['input_ids'][i]
        token_text = tokenizer.decode([token_id])
        
        # Instruction tuning status
        inst_label = inst_data['labels'][i]
        inst_status = "TRAIN" if inst_label != -100 else "MASK"
        
        # Continued pretraining status (always train)
        pretrain_status = "TRAIN"
        
        rows.append({
            'Position': i,
            'Token ID': token_id,
            'Token Text': repr(token_text),
            'Instruction Tuning': inst_status,
            'Continued Pretraining': pretrain_status
        })
    
    df = pd.DataFrame(rows)
    
    # Style the dataframe
    def highlight_status(val):
        if val == 'MASK':
            return 'background-color: #ffcccc; color: #cc0000; font-weight: bold'
        elif val == 'TRAIN':
            return 'background-color: #ccffcc; color: #006600; font-weight: bold'
        return ''
    
    styled_df = df.style.applymap(highlight_status, subset=['Instruction Tuning', 'Continued Pretraining'])
    
    display(styled_df)
    
    return df

# Show token analysis
token_df = show_token_analysis(sample_data, max_tokens=30)

def visualize_masking_pattern(sample_data, max_tokens=100):
    """Create a visual representation of the masking pattern."""
    
    inst_data = sample_data['instruction_tuning']
    
    # Create HTML visualization
    html_parts = []
    html_parts.append("<h3>🎯 Instruction Tuning: Token Masking Visualization</h3>")
    html_parts.append("<p><strong>Legend:</strong> ")
    html_parts.append('<span style="background-color: #ffcccc; padding: 2px 4px; margin: 2px; border-radius: 3px;">MASKED</span> ')
    html_parts.append('<span style="background-color: #ccffcc; padding: 2px 4px; margin: 2px; border-radius: 3px;">TRAINED</span>')
    html_parts.append("</p>")
    
    html_parts.append("<div style='font-family: monospace; line-height: 1.8; word-wrap: break-word;'>")
    
    for i in range(min(max_tokens, len(inst_data['input_ids']))):
        token_id = inst_data['input_ids'][i]
        token_text = tokenizer.decode([token_id])
        label = inst_data['labels'][i]
        
        # Escape HTML characters
        token_text = token_text.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')
        
        if label == -100:  # Masked
            color = "#ffcccc"
            text_color = "#cc0000"
        else:  # Trained
            color = "#ccffcc"
            text_color = "#006600"
        
        html_parts.append(
            f'<span style="background-color: {color}; color: {text_color}; '
            f'padding: 1px 2px; margin: 1px; border-radius: 2px; '
            f'border: 1px solid {text_color}; font-size: 12px;" '
            f'title="Token {i}: {token_id}">{token_text}</span>'
        )
    
    if len(inst_data['input_ids']) > max_tokens:
        html_parts.append(f"<span style='color: #666;'>... ({len(inst_data['input_ids']) - max_tokens} more tokens)</span>")
    
    html_parts.append("</div>")
    
    display(HTML(''.join(html_parts)))

# Visualize masking pattern
visualize_masking_pattern(sample_data, max_tokens=80)

def compare_all_samples():
    """Compare masking statistics across all samples."""
    
    results = []
    
    for i, sample in enumerate(formatted_samples):
        messages = sample["messages"]
        
        # Get instruction tuning data
        input_ids, labels = instruction_pipeline._tokenize_with_masking(messages, tokenizer)
        
        # Calculate statistics
        total_tokens = len(input_ids)
        masked_tokens = sum(1 for l in labels if l == -100)
        trained_tokens = total_tokens - masked_tokens
        training_ratio = trained_tokens / total_tokens * 100
        
        # Get conversation info
        user_content = next((msg['content'] for msg in messages if msg['role'] == 'user'), "")
        assistant_content = next((msg['content'] for msg in messages if msg['role'] == 'assistant'), "")
        
        results.append({
            'Sample': i + 1,
            'Total Tokens': total_tokens,
            'Masked Tokens': masked_tokens,
            'Trained Tokens': trained_tokens,
            'Training Ratio (%)': f"{training_ratio:.1f}%",
            'User Length': len(user_content),
            'Assistant Length': len(assistant_content),
            'Question Preview': user_content[:50] + "..." if len(user_content) > 50 else user_content
        })
    
    df = pd.DataFrame(results)
    
    print("\n📊 COMPARISON ACROSS ALL SAMPLES")
    print("=" * 60)
    display(df)
    
    # Summary statistics
    avg_training_ratio = sum(float(r['Training Ratio (%)'].rstrip('%')) for r in results) / len(results)
    print(f"\n📈 SUMMARY STATISTICS:")
    print(f"Average training ratio: {avg_training_ratio:.1f}%")
    print(f"Average total tokens: {df['Total Tokens'].mean():.0f}")
    print(f"Average masked tokens: {df['Masked Tokens'].mean():.0f}")
    print(f"Average trained tokens: {df['Trained Tokens'].mean():.0f}")
    
    return df

# Compare all samples
comparison_df = compare_all_samples()

# Inspect a different sample
sample_data_2 = inspect_sample(1)
visualize_masking_pattern(sample_data_2, max_tokens=60)