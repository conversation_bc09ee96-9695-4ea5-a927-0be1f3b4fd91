#!/usr/bin/env python3
"""
Test script to check EOS tokens in instruction tuning data.
This shows the last 50 tokens to see if there's an EOS token.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.fine_tune import FineTuningPipeline
from transformers import AutoTokenizer
import json

def test_eos_tokens():
    """Test to check for EOS tokens in instruction tuning data."""
    
    print("🔚 EOS TOKEN ANALYSIS")
    print("=" * 60)
    print("Checking if sequences end with EOS tokens and how they're labeled.")
    
    try:
        # Load sample data
        print("\n📁 Loading sample data...")
        data_file = "data/code_switched_GAIR_LIMO_train_817.jsonl"
        samples = []
        with open(data_file, 'r', encoding='utf-8') as f:
            for i, line in enumerate(f):
                if i >= 2:  # Just load first 2 samples
                    break
                samples.append(json.loads(line.strip()))
        
        print(f"Loaded {len(samples)} samples")
        
        # Initialize instruction tuning pipeline
        print("\n⚙️ Setting up instruction tuning pipeline...")
        instruction_pipeline = FineTuningPipeline(config_path="configs/instruction_tuning.yaml")
        
        # Load tokenizer
        print("\n🔤 Loading tokenizer...")
        model_name = instruction_pipeline.config["model"]["name"]
        tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)
        special_tokens = ["<think>", "</think>"]
        tokenizer.add_tokens(special_tokens)
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        
        print(f"EOS token ID: {tokenizer.eos_token_id}")
        print(f"EOS token text: '{tokenizer.eos_token}'")
        print(f"PAD token ID: {tokenizer.pad_token_id}")
        print(f"PAD token text: '{tokenizer.pad_token}'")
        
        # Format training data
        print("\n📝 Formatting training data...")
        formatted_samples = instruction_pipeline.format_training_data(samples)
        
        # Analyze each sample
        for sample_idx, sample in enumerate(formatted_samples):
            print(f"\n{'='*80}")
            print(f"SAMPLE {sample_idx + 1}: EOS TOKEN ANALYSIS")
            print(f"{'='*80}")
            
            messages = sample["messages"]
            
            # Get instruction tuning tokenization
            input_ids, labels = instruction_pipeline._tokenize_with_masking(messages, tokenizer)
            
            print(f"Total tokens: {len(input_ids)}")
            
            # Show last 20 tokens
            print(f"\n🔚 LAST 20 TOKENS:")
            print("-" * 80)
            print(f"{'Pos':<4} {'Token ID':<8} {'Label':<8} {'Decoded':<20} {'Status':<8} {'EOS?':<4}")
            print("-" * 80)
            
            start_idx = max(0, len(input_ids) - 20)
            for i in range(start_idx, len(input_ids)):
                token_id = input_ids[i]
                label = labels[i]
                token_text = repr(tokenizer.decode([token_id]))
                
                if label == -100:
                    status = "MASKED"
                    label_display = "-100"
                else:
                    status = "TRAINED"
                    label_display = str(label)
                
                # Check if EOS token
                is_eos = "🔚" if token_id == tokenizer.eos_token_id else ""
                
                print(f"{i:<4} {token_id:<8} {label_display:<8} {token_text:<20} {status:<8} {is_eos:<4}")
            
            # EOS analysis
            print(f"\n🔍 EOS ANALYSIS:")
            print("-" * 40)
            
            # Check if ends with EOS
            if len(input_ids) > 0 and input_ids[-1] == tokenizer.eos_token_id:
                print("✅ Sequence ENDS with EOS token")
                print(f"   Last token: {input_ids[-1]} = '{tokenizer.decode([input_ids[-1]])}'")
                print(f"   Label: {labels[-1]} ({'MASKED' if labels[-1] == -100 else 'TRAINED'})")
            else:
                print("⚠️  Sequence does NOT end with EOS token")
                if len(input_ids) > 0:
                    print(f"   Last token: {input_ids[-1]} = '{tokenizer.decode([input_ids[-1]])}'")
                    print(f"   Label: {labels[-1]} ({'MASKED' if labels[-1] == -100 else 'TRAINED'})")
            
            # Find all EOS tokens
            eos_positions = [i for i, token_id in enumerate(input_ids) if token_id == tokenizer.eos_token_id]
            if eos_positions:
                print(f"\n🎯 EOS tokens found at positions: {eos_positions}")
                for pos in eos_positions:
                    label_status = 'MASKED' if labels[pos] == -100 else 'TRAINED'
                    print(f"   Position {pos}: label = {labels[pos]} ({label_status})")
            else:
                print(f"\n❌ No EOS tokens found in the entire sequence")
            
            # Check what the chat template produces
            full_text = tokenizer.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=False
            )
            print(f"\n📝 Chat template ends with: '{full_text[-50:]}'")
        
        print(f"\n{'='*80}")
        print("💡 KEY INSIGHTS:")
        print("=" * 40)
        print("- Check if sequences end with EOS tokens")
        print("- See if EOS tokens are MASKED or TRAINED")
        print("- Understand how chat templates handle sequence endings")
        print("- Verify proper tokenization for instruction tuning")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_eos_tokens()
