#!/usr/bin/env python3
"""
KULLM Pro Fine-tuning Module

This module provides fine-tuning functionality for reasoning models using LoRA (Low-Rank Adaptation)
with think tokens. It supports Accelerate for distributed training, Weights & Biases for experiment
tracking, and proper checkpoint management.

Features:
- LoRA fine-tuning for efficient parameter updates
- Think token integration for reasoning models
- Accelerate library for distributed training optimization
- Weights & Biases integration for experiment tracking
- Checkpoint saving and resumable training
- Configuration-based training parameters
- Python Fire CLI interface

Example usage:
    python src/fine_tune.py --data_file="path/to/training_data.jsonl" --model_name="Qwen/Qwen2.5-7B-Instruct"
    python src/fine_tune.py --data_file="./data/train.jsonl" --output_dir="./outputs/my_model"
"""

import json
import logging
import os
import yaml
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List
import fire
import torch
import wandb
from dotenv import load_dotenv
from datasets import Dataset
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    DataCollatorForLanguageModeling,
    DataCollatorForSeq2Seq,
)
from peft import LoraConfig, get_peft_model, TaskType, prepare_model_for_kbit_training
from accelerate import Accelerator
from tqdm import tqdm

import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.data_processing import load_jsonl, validate_jsonl_format
from utils.model_utils import (
    setup_tokenizer_with_think_tokens,
    load_model_and_tokenizer,
    print_model_info,
    check_gpu_availability,
)
from utils.clean_tokenizer import clean_tokenizer_for_training

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class FineTuningPipeline:
    """
    Pipeline for fine-tuning reasoning models with LoRA and think tokens.
    """

    def __init__(self, config_path: str = "config.yaml"):
        """
        Initialize the fine-tuning pipeline.

        Args:
            config_path: Path to configuration file
        """
        self.config = self.load_config(config_path)
        self.accelerator = Accelerator()

        # Setup wandb if enabled
        if self.config.get("wandb", {}).get("enabled", True):
            wandb_api_key = os.getenv("WANDB_API_KEY")
            if wandb_api_key:
                wandb.login(key=wandb_api_key)
            else:
                logger.warning("WANDB_API_KEY not found. Wandb logging may not work.")

    def load_config(self, config_path: str) -> Dict[str, Any]:
        """
        Load configuration from YAML file.

        Args:
            config_path: Path to config file

        Returns:
            Configuration dictionary
        """
        try:
            with open(config_path, "r", encoding="utf-8") as f:
                config = yaml.safe_load(f)
            logger.info(f"Loaded configuration from {config_path}")

            # Ensure numeric values are properly typed
            config = self._ensure_numeric_types(config)
            return config
        except FileNotFoundError:
            logger.warning(
                f"Config file not found: {config_path}. Using default config."
            )
            return self.get_default_config()
        except Exception as e:
            logger.error(f"Error loading config: {e}. Using default config.")
            return self.get_default_config()

    def _ensure_numeric_types(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Ensure numeric configuration values have correct types.

        Args:
            config: Configuration dictionary

        Returns:
            Configuration with corrected types
        """
        # Training parameters that should be floats
        float_params = ["learning_rate", "weight_decay", "warmup_ratio"]

        # Training parameters that should be integers
        int_params = [
            "num_train_epochs",
            "per_device_train_batch_size",
            "per_device_eval_batch_size",
            "gradient_accumulation_steps",
            "save_steps",
            "eval_steps",
            "logging_steps",
            "save_total_limit",
        ]

        # LoRA parameters that should be floats
        lora_float_params = ["dropout"]

        # LoRA parameters that should be integers
        lora_int_params = ["r", "alpha"]

        # Convert training parameters
        if "training" in config:
            for param in float_params:
                if param in config["training"]:
                    config["training"][param] = float(config["training"][param])

            for param in int_params:
                if param in config["training"]:
                    config["training"][param] = int(config["training"][param])

        # Convert LoRA parameters
        if "lora" in config:
            for param in lora_float_params:
                if param in config["lora"]:
                    config["lora"][param] = float(config["lora"][param])

            for param in lora_int_params:
                if param in config["lora"]:
                    config["lora"][param] = int(config["lora"][param])

        return config

    def get_default_config(self) -> Dict[str, Any]:
        """
        Get default configuration.

        Returns:
            Default configuration dictionary
        """
        return {
            "model": {
                "name": "Qwen/Qwen2.5-7B-Instruct",
                "max_length": 2048,
                "torch_dtype": "float16",
            },
            "training": {
                "num_train_epochs": 3,
                "per_device_train_batch_size": 2,
                "per_device_eval_batch_size": 2,
                "gradient_accumulation_steps": 8,
                "learning_rate": 2e-4,
                "weight_decay": 0.01,
                "warmup_ratio": 0.1,
                "lr_scheduler_type": "cosine",
                "save_steps": 500,
                "eval_steps": 500,
                "logging_steps": 10,
                "save_total_limit": 3,
                "load_best_model_at_end": True,
                "metric_for_best_model": "eval_loss",
                "greater_is_better": False,
                "evaluation_strategy": "steps",
                "save_strategy": "steps",
                "fp16": True,
                "gradient_checkpointing": True,
                "dataloader_pin_memory": False,
                "remove_unused_columns": False,
            },
            "lora": {
                "r": 16,
                "alpha": 32,
                "dropout": 0.1,
                "bias": "none",
                "task_type": "CAUSAL_LM",
                "target_modules": [
                    "q_proj",
                    "k_proj",
                    "v_proj",
                    "o_proj",
                    "gate_proj",
                    "up_proj",
                    "down_proj",
                ],
            },
            "wandb": {
                "project": "kullm-pro-reasoning",
                "enabled": True,
                "entity": None,
                "tags": ["reasoning", "think-tokens"],
            },
            "dataset": {
                "think_token_start": "<think>",
                "think_token_end": "</think>",
                "instruction_tuning": True,  # Enable proper instruction tuning with masking
            },
        }

    def format_training_data(self, data: List[Dict[str, Any]]) -> List[Dict[str, str]]:
        """
        Format training data for reasoning models with think tokens and chat template.

        For LIMO dataset: Format as chat conversation with think tokens
        For other datasets: Use existing format or apply think token wrapping

        Args:
            data: Raw training data

        Returns:
            Formatted training data
        """
        formatted_data = []
        think_start = self.config["dataset"]["think_token_start"]
        think_end = self.config["dataset"]["think_token_end"]

        for sample in data:
            # Handle LIMO dataset format specifically
            if "solution" in sample and "answer" in sample:
                question = sample.get("question", "").strip()
                solution = sample.get("solution", "").strip()
                answer = sample.get("answer", "").strip()

                if question and solution and answer:
                    # Format as <think>\n\nsolution\n\n</think>\n\nanswer
                    assistant_response = (
                        f"{think_start}\n\n{solution}\n\n{think_end}\n\n{answer}"
                    )

                    # Create chat messages
                    messages = [
                        {"role": "user", "content": question},
                        {"role": "assistant", "content": assistant_response},
                    ]

                    formatted_sample = {
                        "messages": messages,
                        "text": assistant_response,  # For backward compatibility
                    }
                    formatted_data.append(formatted_sample)

            # Handle other formats
            elif "text" in sample:
                formatted_data.append(sample)
            elif "input" in sample and "output" in sample:
                formatted_data.append(sample)
            else:
                logger.warning(f"Unknown sample format: {list(sample.keys())}")

        logger.info(f"Formatted {len(formatted_data)} training samples")
        return formatted_data

    def prepare_dataset(
        self, data: List[Dict[str, Any]], tokenizer: AutoTokenizer
    ) -> Dataset:
        """
        Prepare dataset for training with proper instruction tuning (masking system/user prompts).

        Args:
            data: Formatted training data
            tokenizer: Tokenizer to use

        Returns:
            Prepared dataset
        """

        def tokenize_function(examples):
            input_ids_list = []
            labels_list = []

            # Process each example
            for i in range(len(examples.get("messages", examples.get("text", [])))):
                if "messages" in examples and i < len(examples["messages"]):
                    messages = examples["messages"][i]

                    # Check if instruction tuning masking is enabled
                    if self.config["dataset"].get("instruction_tuning", True):
                        # Use proper instruction tuning with masking
                        input_ids, labels = self._tokenize_with_masking(
                            messages, tokenizer
                        )
                    else:
                        # Fallback to old behavior (continued pretraining)
                        text = tokenizer.apply_chat_template(
                            messages, tokenize=False, add_generation_prompt=False
                        )
                        tokenized = tokenizer(
                            text,
                            truncation=True,
                            padding=False,
                            max_length=self.config["model"]["max_length"],
                            return_tensors=None,
                        )
                        input_ids = tokenized["input_ids"]
                        labels = tokenized["input_ids"].copy()

                    input_ids_list.append(input_ids)
                    labels_list.append(labels)
                elif "text" in examples and i < len(examples["text"]):
                    # Fallback: tokenize text directly (no masking for backward compatibility)
                    text = examples["text"][i]
                    tokenized = tokenizer(
                        text,
                        truncation=True,
                        padding=False,
                        max_length=self.config["model"]["max_length"],
                        return_tensors=None,
                    )
                    input_ids_list.append(tokenized["input_ids"])
                    labels_list.append(tokenized["input_ids"].copy())
                else:
                    # Empty fallback
                    input_ids_list.append([])
                    labels_list.append([])

            return {
                "input_ids": input_ids_list,
                "labels": labels_list,
            }

        # Convert to HuggingFace dataset
        dataset = Dataset.from_list(data)

        # Tokenize the dataset
        tokenized_dataset = dataset.map(
            tokenize_function,
            batched=True,
            remove_columns=dataset.column_names,
            desc="Tokenizing dataset with instruction tuning masking",
        )

        logger.info(f"Prepared dataset with {len(tokenized_dataset)} samples")
        return tokenized_dataset

    def _tokenize_with_masking(
        self, messages: List[Dict[str, str]], tokenizer: AutoTokenizer
    ):
        """
        Tokenize messages with proper instruction tuning masking.
        Only assistant responses are used for loss calculation (labels != -100).
        System and user messages are masked with -100.

        Args:
            messages: List of message dictionaries with 'role' and 'content'
            tokenizer: Tokenizer to use

        Returns:
            Tuple of (input_ids, labels) where labels mask non-assistant tokens with -100
        """
        # Apply chat template to get the full conversation text
        full_text = tokenizer.apply_chat_template(
            messages, tokenize=False, add_generation_prompt=False
        )

        # Tokenize the full conversation
        full_tokenized = tokenizer(
            full_text,
            truncation=True,
            padding=False,
            max_length=self.config["model"]["max_length"],
            return_tensors=None,
        )

        input_ids = full_tokenized["input_ids"]
        labels = [-100] * len(input_ids)  # Start with all tokens masked

        # More robust approach: tokenize each message separately and find positions
        current_text_pos = 0

        for message in messages:
            if message["role"] == "assistant":
                content = message["content"]

                # Find where this assistant content appears in the full text
                content_start_pos = full_text.find(content, current_text_pos)

                if content_start_pos != -1:
                    # Get text before this content to find token position
                    prefix_text = full_text[:content_start_pos]

                    # Tokenize prefix to find starting token index
                    if prefix_text:
                        prefix_tokens = tokenizer(
                            prefix_text, add_special_tokens=False
                        )["input_ids"]
                        start_token_idx = len(prefix_tokens)
                    else:
                        start_token_idx = 0

                    # Tokenize the assistant content to find how many tokens it spans
                    content_tokens = tokenizer(content, add_special_tokens=False)[
                        "input_ids"
                    ]
                    end_token_idx = start_token_idx + len(content_tokens)

                    # Ensure we don't exceed the actual input length
                    end_token_idx = min(end_token_idx, len(input_ids))

                    # Unmask the assistant response tokens
                    for idx in range(start_token_idx, end_token_idx):
                        if idx < len(labels):
                            labels[idx] = input_ids[idx]

                    # Update position for next search
                    current_text_pos = content_start_pos + len(content)

        return input_ids, labels

    def setup_lora_model(self, model: AutoModelForCausalLM) -> AutoModelForCausalLM:
        """
        Setup LoRA configuration for the model.

        Args:
            model: Base model

        Returns:
            LoRA-enabled model
        """
        # Enable gradient checkpointing if specified
        if self.config["training"]["gradient_checkpointing"]:
            model.gradient_checkpointing_enable()

        # Prepare model for k-bit training if using quantization
        if hasattr(model, "config") and getattr(
            model.config, "quantization_config", None
        ):
            model = prepare_model_for_kbit_training(model)

        # Setup LoRA configuration
        lora_config = LoraConfig(
            r=self.config["lora"]["r"],
            lora_alpha=self.config["lora"]["alpha"],
            lora_dropout=self.config["lora"]["dropout"],
            bias=self.config["lora"]["bias"],
            task_type=TaskType.CAUSAL_LM,
            target_modules=self.config["lora"]["target_modules"],
        )

        # Apply LoRA to model
        model = get_peft_model(model, lora_config)

        # Ensure model is in training mode
        model.train()

        # Print trainable parameters
        model.print_trainable_parameters()

        return model

    def train(
        self,
        data_file: str,
        output_dir: str,
        model_name: Optional[str] = None,
        run_name: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Train the model with LoRA fine-tuning.

        Args:
            data_file: Path to training data JSONL file
            output_dir: Output directory for trained model
            model_name: Model name (overrides config)
            run_name: Wandb run name

        Returns:
            Training information dictionary
        """
        logger.info("Starting fine-tuning")
        logger.info(f"Training file: {data_file}")
        logger.info(f"Output directory: {output_dir}")
        logger.info(
            f"Training type: {'LoRA fine-tuning' if self.config['lora'].get('enabled', True) else 'Full fine-tuning'}"
        )

        # Log instruction tuning mode
        instruction_tuning_enabled = self.config["dataset"].get(
            "instruction_tuning", True
        )
        training_mode = (
            "Instruction Tuning"
            if instruction_tuning_enabled
            else "Continued Pretraining"
        )
        logger.info(f"Training mode: {training_mode}")
        if instruction_tuning_enabled:
            logger.info(
                "✅ Using proper instruction tuning with system/user prompt masking"
            )
        else:
            logger.info("⚠️  Using continued pretraining (all tokens trained)")

        # Check GPU availability
        gpu_info = check_gpu_availability()
        logger.info(f"GPU Info: {gpu_info}")

        # Load training data
        train_data = load_jsonl(data_file)
        if not validate_jsonl_format(train_data):
            raise ValueError("Invalid training data format")

        # Use model name from parameter or config
        model_name = model_name or self.config["model"]["name"]

        # Load model and tokenizer
        model, tokenizer = load_model_and_tokenizer(
            model_name,
            think_start=self.config["dataset"]["think_token_start"],
            think_end=self.config["dataset"]["think_token_end"],
        )

        # Print model information
        print_model_info(model, tokenizer)

        # Setup LoRA if enabled, otherwise prepare for full fine-tuning
        is_lora_enabled = self.config["lora"].get("enabled", True)
        training_type = "lora" if is_lora_enabled else "full"

        if is_lora_enabled:
            model = self.setup_lora_model(model)
        else:
            # Full fine-tuning: enable gradient checkpointing if specified
            if self.config["training"]["gradient_checkpointing"]:
                model.gradient_checkpointing_enable()
            model.train()
            print(
                f"Full fine-tuning enabled - all {model.num_parameters():,} parameters will be trained"
            )

        # Format training data
        formatted_train_data = self.format_training_data(train_data)
        train_dataset = self.prepare_dataset(formatted_train_data, tokenizer)

        # Setup training arguments
        training_args = TrainingArguments(
            output_dir=output_dir,
            num_train_epochs=self.config["training"]["num_train_epochs"],
            per_device_train_batch_size=self.config["training"][
                "per_device_train_batch_size"
            ],
            gradient_accumulation_steps=self.config["training"][
                "gradient_accumulation_steps"
            ],
            learning_rate=self.config["training"]["learning_rate"],
            weight_decay=self.config["training"]["weight_decay"],
            warmup_ratio=self.config["training"]["warmup_ratio"],
            lr_scheduler_type=self.config["training"]["lr_scheduler_type"],
            save_steps=self.config["training"]["save_steps"],
            logging_steps=self.config["training"]["logging_steps"],
            save_total_limit=self.config["training"]["save_total_limit"],
            save_strategy=self.config["training"]["save_strategy"],
            fp16=self.config["training"]["fp16"],
            gradient_checkpointing=False,  # Handled manually in LoRA setup
            dataloader_pin_memory=self.config["training"]["dataloader_pin_memory"],
            remove_unused_columns=self.config["training"]["remove_unused_columns"],
            report_to="wandb" if self.config["wandb"]["enabled"] else None,
            run_name=run_name
            or f"kullm-pro-{training_type}-{datetime.now().strftime('%Y%m%d-%H%M%S')}",
        )

        # Setup data collator with proper padding
        data_collator = DataCollatorForSeq2Seq(
            tokenizer=tokenizer,
            model=model,
            padding=True,
            pad_to_multiple_of=8,  # Pad to multiple of 8 for efficiency
        )

        # Initialize wandb if enabled
        if self.config["wandb"]["enabled"]:
            # Prepare tags with training type
            base_tags = self.config["wandb"].get("tags", [])
            training_tags = base_tags + [training_type, f"{training_type}-fine-tuning"]

            # Prepare config with training type info
            wandb_config = {
                "model_name": model_name,
                "training_type": training_type,
                "is_lora": is_lora_enabled,
                "training_config": self.config["training"],
                "dataset_size": len(train_dataset),
            }

            # Add LoRA config only if using LoRA
            if is_lora_enabled:
                wandb_config["lora_config"] = self.config["lora"]

            wandb.init(
                project=self.config["wandb"]["project"],
                entity=self.config["wandb"].get("entity"),
                name=training_args.run_name,
                tags=training_tags,
                config=wandb_config,
            )

        # Create trainer
        trainer = Trainer(
            model=model,
            args=training_args,
            train_dataset=train_dataset,
            data_collator=data_collator,
            tokenizer=tokenizer,
        )

        # Start training
        logger.info("Starting training...")
        train_result = trainer.train()

        # Save the final model
        trainer.save_model()
        trainer.save_state()

        # Save tokenizer
        tokenizer.save_pretrained(output_dir)

        # Log final metrics
        logger.info("Training completed!")
        logger.info(f"Final training loss: {train_result.training_loss}")

        # Finish wandb run
        if self.config["wandb"]["enabled"]:
            wandb.finish()

        return {
            "model_path": output_dir,
            "training_loss": train_result.training_loss,
            "training_steps": train_result.global_step,
            "status": "completed",
        }


class FineTuneCLI:
    """
    Command-line interface for fine-tuning using Python Fire.
    """

    def __init__(self):
        """Initialize the CLI."""
        self.logger = logger

    def train(
        self,
        data_file: str,
        output_dir: str = "./outputs/kullm_pro_model",
        model_name: Optional[str] = None,
        config: str = "config.yaml",
        run_name: Optional[str] = None,
    ):
        """
        Train a model with LoRA fine-tuning.

        Args:
            data_file: Path to training data JSONL file
            output_dir: Output directory for trained model (default: "./outputs/kullm_pro_model")
            model_name: Model name to fine-tune (overrides config)
            config: Path to configuration file (default: "config.yaml")
            run_name: Wandb run name (auto-generated if not provided)

        Example:
            python src/fine_tune.py train --data_file="./data/train.jsonl" --output_dir="./outputs/my_model" --config="configs/train_with_think_tokens.yaml"
        """
        # Create pipeline
        pipeline = FineTuningPipeline(config_path=config)

        # Run training
        try:
            result = pipeline.train(
                data_file=data_file,
                output_dir=output_dir,
                model_name=model_name,
                run_name=run_name,
            )

            self.logger.info("Fine-tuning completed successfully!")
            self.logger.info(f"Model saved to: {result['model_path']}")
            self.logger.info(f"Training loss: {result['training_loss']}")

            return result

        except Exception as e:
            self.logger.error(f"Fine-tuning failed: {e}")
            raise


def train_main():
    """Entry point for kullm-train console command."""
    import fire

    fire.Fire(FineTuneCLI().train)


def main():
    """Main entry point for the CLI."""
    fire.Fire(FineTuneCLI)


if __name__ == "__main__":
    main()
