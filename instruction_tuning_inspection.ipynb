import sys
import os
sys.path.append(os.path.dirname(os.path.abspath('.')))

from src.fine_tune import FineTuningPipeline
from transformers import AutoTokenizer
import json
import torch
import pandas as pd
from IPython.display import display, HTML
import numpy as np

# Load a few samples from the training data
data_file = "data/code_switched_GAIR_LIMO_train_817.jsonl"

samples = []
with open(data_file, 'r', encoding='utf-8') as f:
    for i, line in enumerate(f):
        if i >= 3:  # Just load first 3 samples
            break
        samples.append(json.loads(line.strip()))

print(f"Loaded {len(samples)} samples")
print("\nFirst sample structure:")
print(json.dumps(samples[0], indent=2, ensure_ascii=False)[:500] + "...")

# Initialize instruction tuning pipeline
instruction_pipeline = FineTuningPipeline(config_path="configs/instruction_tuning.yaml")

print("Instruction tuning enabled:", instruction_pipeline.config["dataset"].get("instruction_tuning", True))
print("This means system/user prompts will be masked with -100")

# Load tokenizer
model_name = instruction_pipeline.config["model"]["name"]
tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)

# Add special tokens
special_tokens = ["<think>", "</think>"]
tokenizer.add_tokens(special_tokens)

if tokenizer.pad_token is None:
    tokenizer.pad_token = tokenizer.eos_token

print(f"Tokenizer loaded: {len(tokenizer)} tokens")
print(f"Special tokens added: {special_tokens}")

# Format the sample data
formatted_samples = instruction_pipeline.format_training_data(samples)

print(f"Formatted {len(formatted_samples)} samples")
print("\nFormatted sample structure:")
sample = formatted_samples[0]
print("Messages:")
for msg in sample["messages"]:
    print(f"  {msg['role'].upper()}: {msg['content'][:100]}...")

def inspect_instruction_tuning_sample(sample_idx=0):
    """Inspect a specific sample showing exactly what goes into the model."""
    
    sample = formatted_samples[sample_idx]
    messages = sample["messages"]
    
    print(f"\n{'='*80}")
    print(f"INSTRUCTION TUNING SAMPLE {sample_idx + 1} INSPECTION")
    print(f"{'='*80}")
    
    # Show original conversation
    print("\n📋 ORIGINAL CONVERSATION:")
    print("-" * 40)
    for msg in messages:
        print(f"{msg['role'].upper()}: {msg['content'][:200]}{'...' if len(msg['content']) > 200 else ''}")
        print()
    
    # Apply chat template
    full_text = tokenizer.apply_chat_template(
        messages, tokenize=False, add_generation_prompt=False
    )
    
    print("\n💬 CHAT TEMPLATE OUTPUT:")
    print("-" * 40)
    print(f"Length: {len(full_text)} characters")
    print(f"Text: {full_text[:300]}{'...' if len(full_text) > 300 else ''}")
    
    # Get instruction tuning tokenization with masking
    input_ids, labels = instruction_pipeline._tokenize_with_masking(messages, tokenizer)
    
    print("\n\n🎯 INSTRUCTION TUNING TOKENIZATION:")
    print("=" * 50)
    print(f"Input tokens: {len(input_ids)}")
    print(f"Masked tokens (labels = -100): {sum(1 for l in labels if l == -100)}")
    print(f"Trained tokens (labels = token_id): {sum(1 for l in labels if l != -100)}")
    print(f"Training ratio: {sum(1 for l in labels if l != -100) / len(labels) * 100:.1f}%")
    
    return {
        'messages': messages,
        'full_text': full_text,
        'input_ids': input_ids,
        'labels': labels
    }

# Inspect first sample
sample_data = inspect_instruction_tuning_sample(0)

def show_detailed_token_analysis(sample_data, max_tokens=50):
    """Show detailed token-by-token analysis with decoded values and -100 labels."""
    
    input_ids = sample_data['input_ids']
    labels = sample_data['labels']
    
    print(f"\n🔍 DETAILED TOKEN ANALYSIS (First {max_tokens} tokens)")
    print("=" * 100)
    print("This shows exactly what goes into the model:")
    print("- input_ids: The actual tokens fed to the model")
    print("- labels: What the model tries to predict (-100 = masked, token_id = trained)")
    print("=" * 100)
    
    # Create detailed analysis table
    rows = []
    
    for i in range(min(max_tokens, len(input_ids))):
        token_id = input_ids[i]
        label = labels[i]
        token_text = tokenizer.decode([token_id])
        
        # Determine status
        if label == -100:
            status = "MASKED"
            label_display = "-100"
        else:
            status = "TRAINED"
            label_display = str(label)
        
        rows.append({
            'Position': i,
            'Input Token ID': token_id,
            'Decoded Text': repr(token_text),
            'Label': label_display,
            'Status': status,
            'Contributes to Loss': "❌" if label == -100 else "✅"
        })
    
    df = pd.DataFrame(rows)
    
    # Style the dataframe
    def highlight_status(val):
        if val == 'MASKED':
            return 'background-color: #ffcccc; color: #cc0000; font-weight: bold'
        elif val == 'TRAINED':
            return 'background-color: #ccffcc; color: #006600; font-weight: bold'
        return ''
    
    def highlight_label(val):
        if val == '-100':
            return 'background-color: #ffcccc; color: #cc0000; font-weight: bold'
        return 'background-color: #ccffcc; color: #006600'
    
    styled_df = df.style.applymap(highlight_status, subset=['Status']) \
                      .applymap(highlight_label, subset=['Label'])
    
    display(styled_df)
    
    return df

# Show detailed token analysis
token_df = show_detailed_token_analysis(sample_data, max_tokens=100)

def visualize_instruction_tuning_masking(sample_data, max_tokens=80):
    """Create a visual representation of the instruction tuning masking pattern."""
    
    input_ids = sample_data['input_ids']
    labels = sample_data['labels']
    
    # Create HTML visualization
    html_parts = []
    html_parts.append("<h3>🎯 Instruction Tuning: Token Masking Visualization</h3>")
    html_parts.append("<p><strong>Legend:</strong> ")
    html_parts.append('<span style="background-color: #ffcccc; padding: 2px 4px; margin: 2px; border-radius: 3px; color: #cc0000; font-weight: bold;">MASKED (-100)</span> ')
    html_parts.append('<span style="background-color: #ccffcc; padding: 2px 4px; margin: 2px; border-radius: 3px; color: #006600; font-weight: bold;">TRAINED (token_id)</span>')
    html_parts.append("</p>")
    html_parts.append("<p><em>Red tokens have labels = -100 and don't contribute to loss. Green tokens have labels = token_id and are used for training.</em></p>")
    
    html_parts.append("<div style='font-family: monospace; line-height: 2.0; word-wrap: break-word; border: 1px solid #ccc; padding: 10px; background-color: #f9f9f9;'>")
    
    for i in range(min(max_tokens, len(input_ids))):
        token_id = input_ids[i]
        label = labels[i]
        token_text = tokenizer.decode([token_id])
        
        # Escape HTML characters
        token_text = token_text.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')
        
        if label == -100:  # Masked
            color = "#ffcccc"
            text_color = "#cc0000"
            title = f"Token {i}: input_id={token_id}, label=-100 (MASKED)"
        else:  # Trained
            color = "#ccffcc"
            text_color = "#006600"
            title = f"Token {i}: input_id={token_id}, label={label} (TRAINED)"
        
        html_parts.append(
            f'<span style="background-color: {color}; color: {text_color}; '
            f'padding: 2px 4px; margin: 1px; border-radius: 3px; '
            f'border: 1px solid {text_color}; font-size: 13px; font-weight: bold;" '
            f'title="{title}">{token_text}</span>'
        )
    
    if len(input_ids) > max_tokens:
        html_parts.append(f"<br><span style='color: #666; font-style: italic;'>... ({len(input_ids) - max_tokens} more tokens)</span>")
    
    html_parts.append("</div>")
    
    # Add statistics
    masked_count = sum(1 for l in labels if l == -100)
    trained_count = len(labels) - masked_count
    html_parts.append(f"<p><strong>Statistics:</strong></p>")
    html_parts.append(f"<ul>")
    html_parts.append(f"<li>Total tokens: {len(input_ids)}</li>")
    html_parts.append(f"<li><span style='color: #cc0000;'>Masked tokens: {masked_count}</span></li>")
    html_parts.append(f"<li><span style='color: #006600;'>Trained tokens: {trained_count}</span></li>")
    html_parts.append(f"<li>Training ratio: {trained_count/len(labels)*100:.1f}%</li>")
    html_parts.append(f"</ul>")
    
    display(HTML(''.join(html_parts)))

# Visualize masking pattern
visualize_instruction_tuning_masking(sample_data, max_tokens=60)

def analyze_all_samples():
    """Analyze masking patterns across all samples."""
    
    results = []
    
    for i, sample in enumerate(formatted_samples):
        messages = sample["messages"]
        
        # Get instruction tuning data
        input_ids, labels = instruction_pipeline._tokenize_with_masking(messages, tokenizer)
        
        # Calculate statistics
        total_tokens = len(input_ids)
        masked_tokens = sum(1 for l in labels if l == -100)
        trained_tokens = total_tokens - masked_tokens
        training_ratio = trained_tokens / total_tokens * 100
        
        # Get conversation info
        user_content = next((msg['content'] for msg in messages if msg['role'] == 'user'), "")
        assistant_content = next((msg['content'] for msg in messages if msg['role'] == 'assistant'), "")
        
        results.append({
            'Sample': i + 1,
            'Total Tokens': total_tokens,
            'Masked (-100)': masked_tokens,
            'Trained (token_id)': trained_tokens,
            'Training Ratio': f"{training_ratio:.1f}%",
            'User Chars': len(user_content),
            'Assistant Chars': len(assistant_content),
            'Question Preview': user_content[:40] + "..." if len(user_content) > 40 else user_content
        })
    
    df = pd.DataFrame(results)
    
    print("\n📊 INSTRUCTION TUNING ANALYSIS ACROSS ALL SAMPLES")
    print("=" * 70)
    display(df)
    
    # Summary statistics
    avg_training_ratio = sum(float(r['Training Ratio'].rstrip('%')) for r in results) / len(results)
    print(f"\n📈 SUMMARY STATISTICS:")
    print(f"Average training ratio: {avg_training_ratio:.1f}%")
    print(f"Average total tokens: {df['Total Tokens'].mean():.0f}")
    print(f"Average masked tokens: {df['Masked (-100)'].mean():.0f}")
    print(f"Average trained tokens: {df['Trained (token_id)'].mean():.0f}")
    
    return df

# Analyze all samples
analysis_df = analyze_all_samples()

# Inspect a different sample
sample_data_2 = inspect_instruction_tuning_sample(1)
visualize_instruction_tuning_masking(sample_data_2, max_tokens=50)