{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Instruction Tuning Data Inspection\n", "\n", "This notebook shows exactly what data goes into the model during **instruction tuning**, including:\n", "- Raw input tokens and their decoded text\n", "- Label masking (-100 for system/user, actual token IDs for assistant)\n", "- Visual representation of which tokens are masked vs trained\n", "- Detailed token-by-token analysis with decoded values\n", "\n", "**Key Focus**: Understanding how system and user prompts are masked with -100 while only assistant responses contribute to the loss."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "sys.path.append(os.path.dirname(os.path.abspath('.')))\n", "\n", "from src.fine_tune import FineTuningPipeline\n", "from transformers import AutoTokenizer\n", "import json\n", "import torch\n", "import pandas as pd\n", "from IPython.display import display, HTML\n", "import numpy as np"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Sample Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load a few samples from the training data\n", "data_file = \"data/code_switched_GAIR_LIMO_train_817.jsonl\"\n", "\n", "samples = []\n", "with open(data_file, 'r', encoding='utf-8') as f:\n", "    for i, line in enumerate(f):\n", "        if i >= 3:  # Just load first 3 samples\n", "            break\n", "        samples.append(json.loads(line.strip()))\n", "\n", "print(f\"Loaded {len(samples)} samples\")\n", "print(\"\\nFirst sample structure:\")\n", "print(json.dumps(samples[0], indent=2, ensure_ascii=False)[:500] + \"...\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Initialize Instruction Tuning Pipeline"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize instruction tuning pipeline\n", "instruction_pipeline = FineTuningPipeline(config_path=\"configs/instruction_tuning.yaml\")\n", "\n", "print(\"Instruction tuning enabled:\", instruction_pipeline.config[\"dataset\"].get(\"instruction_tuning\", True))\n", "print(\"This means system/user prompts will be masked with -100\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load tokenizer\n", "model_name = instruction_pipeline.config[\"model\"][\"name\"]\n", "tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)\n", "\n", "# Add special tokens\n", "special_tokens = [\"<think>\", \"</think>\"]\n", "tokenizer.add_tokens(special_tokens)\n", "\n", "if tokenizer.pad_token is None:\n", "    tokenizer.pad_token = tokenizer.eos_token\n", "\n", "print(f\"Tokenizer loaded: {len(tokenizer)} tokens\")\n", "print(f\"Special tokens added: {special_tokens}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Format Training Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Format the sample data\n", "formatted_samples = instruction_pipeline.format_training_data(samples)\n", "\n", "print(f\"Formatted {len(formatted_samples)} samples\")\n", "print(\"\\nFormatted sample structure:\")\n", "sample = formatted_samples[0]\n", "print(\"Messages:\")\n", "for msg in sample[\"messages\"]:\n", "    print(f\"  {msg['role'].upper()}: {msg['content'][:100]}...\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Detailed Token Analysis with -100 Labels"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def inspect_instruction_tuning_sample(sample_idx=0):\n", "    \"\"\"Inspect a specific sample showing exactly what goes into the model.\"\"\"\n", "    \n", "    sample = formatted_samples[sample_idx]\n", "    messages = sample[\"messages\"]\n", "    \n", "    print(f\"\\n{'='*80}\")\n", "    print(f\"INSTRUCTION TUNING SAMPLE {sample_idx + 1} INSPECTION\")\n", "    print(f\"{'='*80}\")\n", "    \n", "    # Show original conversation\n", "    print(\"\\n📋 ORIGINAL CONVERSATION:\")\n", "    print(\"-\" * 40)\n", "    for msg in messages:\n", "        print(f\"{msg['role'].upper()}: {msg['content'][:200]}{'...' if len(msg['content']) > 200 else ''}\")\n", "        print()\n", "    \n", "    # Apply chat template\n", "    full_text = tokenizer.apply_chat_template(\n", "        messages, tokenize=False, add_generation_prompt=False\n", "    )\n", "    \n", "    print(\"\\n💬 CHAT TEMPLATE OUTPUT:\")\n", "    print(\"-\" * 40)\n", "    print(f\"Length: {len(full_text)} characters\")\n", "    print(f\"Text: {full_text[:300]}{'...' if len(full_text) > 300 else ''}\")\n", "    \n", "    # Get instruction tuning tokenization with masking\n", "    input_ids, labels = instruction_pipeline._tokenize_with_masking(messages, tokenizer)\n", "    \n", "    print(\"\\n\\n🎯 INSTRUCTION TUNING TOKENIZATION:\")\n", "    print(\"=\" * 50)\n", "    print(f\"Input tokens: {len(input_ids)}\")\n", "    print(f\"Masked tokens (labels = -100): {sum(1 for l in labels if l == -100)}\")\n", "    print(f\"Trained tokens (labels = token_id): {sum(1 for l in labels if l != -100)}\")\n", "    print(f\"Training ratio: {sum(1 for l in labels if l != -100) / len(labels) * 100:.1f}%\")\n", "    \n", "    return {\n", "        'messages': messages,\n", "        'full_text': full_text,\n", "        'input_ids': input_ids,\n", "        'labels': labels\n", "    }\n", "\n", "# Inspect first sample\n", "sample_data = inspect_instruction_tuning_sample(0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Token-by-Token Analysis: Input IDs vs Labels"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def show_detailed_token_analysis(sample_data, max_tokens=50):\n", "    \"\"\"Show detailed token-by-token analysis with decoded values and -100 labels.\"\"\"\n", "    \n", "    input_ids = sample_data['input_ids']\n", "    labels = sample_data['labels']\n", "    \n", "    print(f\"\\n🔍 DETAILED TOKEN ANALYSIS (First {max_tokens} tokens)\")\n", "    print(\"=\" * 100)\n", "    print(\"This shows exactly what goes into the model:\")\n", "    print(\"- input_ids: The actual tokens fed to the model\")\n", "    print(\"- labels: What the model tries to predict (-100 = masked, token_id = trained)\")\n", "    print(\"=\" * 100)\n", "    \n", "    # Create detailed analysis table\n", "    rows = []\n", "    \n", "    for i in range(min(max_tokens, len(input_ids))):\n", "        token_id = input_ids[i]\n", "        label = labels[i]\n", "        token_text = tokenizer.decode([token_id])\n", "        \n", "        # Determine status\n", "        if label == -100:\n", "            status = \"MASKED\"\n", "            label_display = \"-100\"\n", "        else:\n", "            status = \"TRAINED\"\n", "            label_display = str(label)\n", "        \n", "        rows.append({\n", "            'Position': i,\n", "            'Input Token ID': token_id,\n", "            'Decoded Text': repr(token_text),\n", "            'Label': label_display,\n", "            'Status': status,\n", "            'Contributes to Loss': \"❌\" if label == -100 else \"✅\"\n", "        })\n", "    \n", "    df = pd.DataFrame(rows)\n", "    \n", "    # Style the dataframe\n", "    def highlight_status(val):\n", "        if val == 'MASKED':\n", "            return 'background-color: #ffcccc; color: #cc0000; font-weight: bold'\n", "        elif val == 'TRAINED':\n", "            return 'background-color: #ccffcc; color: #006600; font-weight: bold'\n", "        return ''\n", "    \n", "    def highlight_label(val):\n", "        if val == '-100':\n", "            return 'background-color: #ffcccc; color: #cc0000; font-weight: bold'\n", "        return 'background-color: #ccffcc; color: #006600'\n", "    \n", "    styled_df = df.style.applymap(highlight_status, subset=['Status']) \\\n", "                      .applymap(highlight_label, subset=['Label'])\n", "    \n", "    display(styled_df)\n", "    \n", "    return df\n", "\n", "# Show detailed token analysis\n", "token_df = show_detailed_token_analysis(sample_data, max_tokens=100)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Last 50 Tokens Analysis (Check for EOS Token)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def show_last_tokens_analysis(sample_data, num_tokens=50):\n", "    \"\"\"Show the last N tokens to check for EOS token and sequence ending.\"\"\"\n", "    \n", "    input_ids = sample_data['input_ids']\n", "    labels = sample_data['labels']\n", "    total_tokens = len(input_ids)\n", "    \n", "    print(f\"\\n🔚 LAST {num_tokens} TOKENS ANALYSIS (to check for EOS token)\")\n", "    print(\"=\" * 100)\n", "    print(f\"Total tokens in sequence: {total_tokens}\")\n", "    print(f\"Showing tokens {max(0, total_tokens-num_tokens)} to {total_tokens-1}\")\n", "    print(f\"EOS token ID: {tokenizer.eos_token_id} ('{tokenizer.eos_token}')\")\n", "    print(\"=\" * 100)\n", "    \n", "    # Create table for last tokens\n", "    last_token_rows = []\n", "    start_idx = max(0, total_tokens - num_tokens)\n", "    \n", "    for i in range(start_idx, total_tokens):\n", "        token_id = input_ids[i]\n", "        label = labels[i]\n", "        token_text = tokenizer.decode([token_id])\n", "        \n", "        # Check if this is EOS token\n", "        is_eos = token_id == tokenizer.eos_token_id\n", "        eos_marker = \" 🔚 EOS\" if is_eos else \"\"\n", "        \n", "        # Determine status\n", "        if label == -100:\n", "            status = \"MASKED\"\n", "            label_display = \"-100\"\n", "        else:\n", "            status = \"TRAINED\"\n", "            label_display = str(label)\n", "        \n", "        last_token_rows.append({\n", "            'Position': i,\n", "            'Input Token ID': token_id,\n", "            'Decoded Text': repr(token_text) + eos_marker,\n", "            'Label': label_display,\n", "            'Status': status,\n", "            'Contributes to Loss': \"❌\" if label == -100 else \"✅\"\n", "        })\n", "    \n", "    last_df = pd.DataFrame(last_token_rows)\n", "    \n", "    # Style the dataframe\n", "    def highlight_status(val):\n", "        if val == 'MASKED':\n", "            return 'background-color: #ffcccc; color: #cc0000; font-weight: bold'\n", "        elif val == 'TRAINED':\n", "            return 'background-color: #ccffcc; color: #006600; font-weight: bold'\n", "        return ''\n", "    \n", "    def highlight_label(val):\n", "        if val == '-100':\n", "            return 'background-color: #ffcccc; color: #cc0000; font-weight: bold'\n", "        return 'background-color: #ccffcc; color: #006600'\n", "    \n", "    def highlight_eos(val):\n", "        if '🔚 EOS' in str(val):\n", "            return 'background-color: #ffffcc; color: #cc6600; font-weight: bold; border: 2px solid #cc6600'\n", "        return ''\n", "    \n", "    styled_last_df = last_df.style.applymap(highlight_status, subset=['Status']) \\\n", "                                  .applymap(highlight_label, subset=['Label']) \\\n", "                                  .applymap(highlight_eos, subset=['Decoded Text'])\n", "    \n", "    display(styled_last_df)\n", "    \n", "    # Check if sequence ends with EOS\n", "    print(\"\\n🔍 EOS TOKEN ANALYSIS:\")\n", "    print(\"-\" * 50)\n", "    \n", "    if total_tokens > 0 and input_ids[-1] == tokenizer.eos_token_id:\n", "        print(\"✅ Sequence ENDS with EOS token\")\n", "        print(f\"   Last token: {input_ids[-1]} = '{tokenizer.decode([input_ids[-1]])}'\")\n", "        print(f\"   Label: {labels[-1]} ({'MASKED' if labels[-1] == -100 else 'TRAINED'})\")\n", "    else:\n", "        print(\"⚠️  Sequence does NOT end with EOS token\")\n", "        if total_tokens > 0:\n", "            print(f\"   Last token: {input_ids[-1]} = '{tokenizer.decode([input_ids[-1]])}'\")\n", "            print(f\"   Label: {labels[-1]} ({'MASKED' if labels[-1] == -100 else 'TRAINED'})\")\n", "    \n", "    # Check for any EOS tokens in the sequence\n", "    eos_positions = [i for i, token_id in enumerate(input_ids) if token_id == tokenizer.eos_token_id]\n", "    if eos_positions:\n", "        print(f\"\\n🎯 EOS tokens found at positions: {eos_positions}\")\n", "        for pos in eos_positions:\n", "            label_status = 'MASKED' if labels[pos] == -100 else 'TRAINED'\n", "            print(f\"   Position {pos}: label = {labels[pos]} ({label_status})\")\n", "    else:\n", "        print(f\"\\n❌ No EOS tokens found in the entire sequence\")\n", "    \n", "    return last_df\n", "\n", "# Show last 50 tokens analysis\n", "last_tokens_df = show_last_tokens_analysis(sample_data, num_tokens=50)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Compare Multiple Samples"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_all_samples():\n", "    \"\"\"Analyze masking patterns across all samples.\"\"\"\n", "    \n", "    results = []\n", "    \n", "    for i, sample in enumerate(formatted_samples):\n", "        messages = sample[\"messages\"]\n", "        \n", "        # Get instruction tuning data\n", "        input_ids, labels = instruction_pipeline._tokenize_with_masking(messages, tokenizer)\n", "        \n", "        # Calculate statistics\n", "        total_tokens = len(input_ids)\n", "        masked_tokens = sum(1 for l in labels if l == -100)\n", "        trained_tokens = total_tokens - masked_tokens\n", "        training_ratio = trained_tokens / total_tokens * 100\n", "        \n", "        # Get conversation info\n", "        user_content = next((msg['content'] for msg in messages if msg['role'] == 'user'), \"\")\n", "        assistant_content = next((msg['content'] for msg in messages if msg['role'] == 'assistant'), \"\")\n", "        \n", "        results.append({\n", "            'Sample': i + 1,\n", "            'Total Tokens': total_tokens,\n", "            'Masked (-100)': masked_tokens,\n", "            'Trained (token_id)': trained_tokens,\n", "            'Training Ratio': f\"{training_ratio:.1f}%\",\n", "            'User Chars': len(user_content),\n", "            'Assistant Chars': len(assistant_content),\n", "            'Question Preview': user_content[:40] + \"...\" if len(user_content) > 40 else user_content\n", "        })\n", "    \n", "    df = pd.DataFrame(results)\n", "    \n", "    print(\"\\n📊 INSTRUCTION TUNING ANALYSIS ACROSS ALL SAMPLES\")\n", "    print(\"=\" * 70)\n", "    display(df)\n", "    \n", "    # Summary statistics\n", "    avg_training_ratio = sum(float(r['Training Ratio'].rstrip('%')) for r in results) / len(results)\n", "    print(f\"\\n📈 SUMMARY STATISTICS:\")\n", "    print(f\"Average training ratio: {avg_training_ratio:.1f}%\")\n", "    print(f\"Average total tokens: {df['Total Tokens'].mean():.0f}\")\n", "    print(f\"Average masked tokens: {df['Masked (-100)'].mean():.0f}\")\n", "    print(f\"Average trained tokens: {df['Trained (token_id)'].mean():.0f}\")\n", "    \n", "    return df\n", "\n", "# Analyze all samples\n", "analysis_df = analyze_all_samples()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Inspect Another Sample"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Inspect a different sample\n", "sample_data_2 = inspect_instruction_tuning_sample(1)\n", "visualize_instruction_tuning_masking(sample_data_2, max_tokens=50)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Key Insights from Instruction Tuning\n", "\n", "From this analysis, we can see exactly how instruction tuning works:\n", "\n", "### 🎯 **Label Masking Strategy**\n", "- **System and user prompts**: `labels = -100` (masked, don't contribute to loss)\n", "- **Assistant responses**: `labels = token_id` (trained, contribute to loss)\n", "- **Special tokens**: Typically masked depending on position\n", "\n", "### 📊 **Training Efficiency**\n", "- **Training ratio**: ~30-70% of tokens (varies by conversation length)\n", "- **Focus**: Model learns to generate appropriate responses, not predict user inputs\n", "- **Loss calculation**: Only assistant tokens contribute to gradient updates\n", "\n", "### 💡 **Why This Works Better**\n", "- **Prevents overfitting** to user input patterns\n", "- **Focuses learning** on response generation\n", "- **Improves instruction following** capabilities\n", "- **Reduces likelihood** of generating user-like text\n", "\n", "### 🔍 **What to Look For**\n", "- ✅ **Red tokens** should be system/user content\n", "- ✅ **Green tokens** should be assistant responses\n", "- ✅ **Training ratio** should be reasonable (not too low/high)\n", "- ✅ **Clear separation** between masked and trained regions\n", "\n", "### 🔚 **E<PERSON> Findings**\n", "From the analysis, we discovered:\n", "- **EOS tokens (`<|im_end|>`) are present** in the sequence but **MASKED** with -100\n", "- **Sequences are truncated** at max_length (2048) and do **NOT end with EOS**\n", "- **Chat template adds `<|im_end|>`** but it gets masked during instruction tuning\n", "- **This is correct behavior** - we don't want to train on special formatting tokens\n", "\n", "### ⚠️ **Important Notes**\n", "- **Truncation**: Sequences longer than max_length are cut off\n", "- **No explicit EOS at end**: The model learns to stop naturally\n", "- **EOS tokens are masked**: Special tokens don't contribute to loss\n", "- **This is standard practice** for instruction tuning"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 4}