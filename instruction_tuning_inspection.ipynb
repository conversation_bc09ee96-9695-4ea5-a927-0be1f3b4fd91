import sys
import os
sys.path.append(os.path.dirname(os.path.abspath('.')))

from src.fine_tune import FineTuningPipeline
from transformers import AutoTokenizer
import json
import torch
import pandas as pd
from IPython.display import display, HTML
import numpy as np

# Load a few samples from the training data
data_file = "data/code_switched_GAIR_LIMO_train_817.jsonl"

samples = []
with open(data_file, 'r', encoding='utf-8') as f:
    for i, line in enumerate(f):
        if i >= 3:  # Just load first 3 samples
            break
        samples.append(json.loads(line.strip()))

print(f"Loaded {len(samples)} samples")
print("\nFirst sample structure:")
print(json.dumps(samples[0], indent=2, ensure_ascii=False)[:500] + "...")

# Initialize instruction tuning pipeline
instruction_pipeline = FineTuningPipeline(config_path="configs/instruction_tuning.yaml")

print("Instruction tuning enabled:", instruction_pipeline.config["dataset"].get("instruction_tuning", True))
print("This means system/user prompts will be masked with -100")

# Load tokenizer
model_name = instruction_pipeline.config["model"]["name"]
tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)

# Add special tokens
special_tokens = ["<think>", "</think>"]
tokenizer.add_tokens(special_tokens)

if tokenizer.pad_token is None:
    tokenizer.pad_token = tokenizer.eos_token

print(f"Tokenizer loaded: {len(tokenizer)} tokens")
print(f"Special tokens added: {special_tokens}")

# Format the sample data
formatted_samples = instruction_pipeline.format_training_data(samples)

print(f"Formatted {len(formatted_samples)} samples")
print("\nFormatted sample structure:")
sample = formatted_samples[0]
print("Messages:")
for msg in sample["messages"]:
    print(f"  {msg['role'].upper()}: {msg['content'][:100]}...")

def inspect_instruction_tuning_sample(sample_idx=0):
    """Inspect a specific sample showing exactly what goes into the model."""
    
    sample = formatted_samples[sample_idx]
    messages = sample["messages"]
    
    print(f"\n{'='*80}")
    print(f"INSTRUCTION TUNING SAMPLE {sample_idx + 1} INSPECTION")
    print(f"{'='*80}")
    
    # Show original conversation
    print("\n📋 ORIGINAL CONVERSATION:")
    print("-" * 40)
    for msg in messages:
        print(f"{msg['role'].upper()}: {msg['content'][:200]}{'...' if len(msg['content']) > 200 else ''}")
        print()
    
    # Apply chat template
    full_text = tokenizer.apply_chat_template(
        messages, tokenize=False, add_generation_prompt=False
    )
    
    print("\n💬 CHAT TEMPLATE OUTPUT:")
    print("-" * 40)
    print(f"Length: {len(full_text)} characters")
    print(f"Text: {full_text[:300]}{'...' if len(full_text) > 300 else ''}")
    
    # Get instruction tuning tokenization with masking
    input_ids, labels = instruction_pipeline._tokenize_with_masking(messages, tokenizer)
    
    print("\n\n🎯 INSTRUCTION TUNING TOKENIZATION:")
    print("=" * 50)
    print(f"Input tokens: {len(input_ids)}")
    print(f"Masked tokens (labels = -100): {sum(1 for l in labels if l == -100)}")
    print(f"Trained tokens (labels = token_id): {sum(1 for l in labels if l != -100)}")
    print(f"Training ratio: {sum(1 for l in labels if l != -100) / len(labels) * 100:.1f}%")
    
    return {
        'messages': messages,
        'full_text': full_text,
        'input_ids': input_ids,
        'labels': labels
    }

# Inspect first sample
sample_data = inspect_instruction_tuning_sample(0)

def show_detailed_token_analysis(sample_data, max_tokens=50):
    """Show detailed token-by-token analysis with decoded values and -100 labels."""
    
    input_ids = sample_data['input_ids']
    labels = sample_data['labels']
    
    print(f"\n🔍 DETAILED TOKEN ANALYSIS (First {max_tokens} tokens)")
    print("=" * 100)
    print("This shows exactly what goes into the model:")
    print("- input_ids: The actual tokens fed to the model")
    print("- labels: What the model tries to predict (-100 = masked, token_id = trained)")
    print("=" * 100)
    
    # Create detailed analysis table
    rows = []
    
    for i in range(min(max_tokens, len(input_ids))):
        token_id = input_ids[i]
        label = labels[i]
        token_text = tokenizer.decode([token_id])
        
        # Determine status
        if label == -100:
            status = "MASKED"
            label_display = "-100"
        else:
            status = "TRAINED"
            label_display = str(label)
        
        rows.append({
            'Position': i,
            'Input Token ID': token_id,
            'Decoded Text': repr(token_text),
            'Label': label_display,
            'Status': status,
            'Contributes to Loss': "❌" if label == -100 else "✅"
        })
    
    df = pd.DataFrame(rows)
    
    # Style the dataframe
    def highlight_status(val):
        if val == 'MASKED':
            return 'background-color: #ffcccc; color: #cc0000; font-weight: bold'
        elif val == 'TRAINED':
            return 'background-color: #ccffcc; color: #006600; font-weight: bold'
        return ''
    
    def highlight_label(val):
        if val == '-100':
            return 'background-color: #ffcccc; color: #cc0000; font-weight: bold'
        return 'background-color: #ccffcc; color: #006600'
    
    styled_df = df.style.applymap(highlight_status, subset=['Status']) \
                      .applymap(highlight_label, subset=['Label'])
    
    display(styled_df)
    
    return df

# Show detailed token analysis
token_df = show_detailed_token_analysis(sample_data, max_tokens=100)

def show_last_tokens_analysis(sample_data, num_tokens=50):
    """Show the last N tokens to check for EOS token and sequence ending."""
    
    input_ids = sample_data['input_ids']
    labels = sample_data['labels']
    total_tokens = len(input_ids)
    
    print(f"\n🔚 LAST {num_tokens} TOKENS ANALYSIS (to check for EOS token)")
    print("=" * 100)
    print(f"Total tokens in sequence: {total_tokens}")
    print(f"Showing tokens {max(0, total_tokens-num_tokens)} to {total_tokens-1}")
    print(f"EOS token ID: {tokenizer.eos_token_id} ('{tokenizer.eos_token}')")
    print("=" * 100)
    
    # Create table for last tokens
    last_token_rows = []
    start_idx = max(0, total_tokens - num_tokens)
    
    for i in range(start_idx, total_tokens):
        token_id = input_ids[i]
        label = labels[i]
        token_text = tokenizer.decode([token_id])
        
        # Check if this is EOS token
        is_eos = token_id == tokenizer.eos_token_id
        eos_marker = " 🔚 EOS" if is_eos else ""
        
        # Determine status
        if label == -100:
            status = "MASKED"
            label_display = "-100"
        else:
            status = "TRAINED"
            label_display = str(label)
        
        last_token_rows.append({
            'Position': i,
            'Input Token ID': token_id,
            'Decoded Text': repr(token_text) + eos_marker,
            'Label': label_display,
            'Status': status,
            'Contributes to Loss': "❌" if label == -100 else "✅"
        })
    
    last_df = pd.DataFrame(last_token_rows)
    
    # Style the dataframe
    def highlight_status(val):
        if val == 'MASKED':
            return 'background-color: #ffcccc; color: #cc0000; font-weight: bold'
        elif val == 'TRAINED':
            return 'background-color: #ccffcc; color: #006600; font-weight: bold'
        return ''
    
    def highlight_label(val):
        if val == '-100':
            return 'background-color: #ffcccc; color: #cc0000; font-weight: bold'
        return 'background-color: #ccffcc; color: #006600'
    
    def highlight_eos(val):
        if '🔚 EOS' in str(val):
            return 'background-color: #ffffcc; color: #cc6600; font-weight: bold; border: 2px solid #cc6600'
        return ''
    
    styled_last_df = last_df.style.applymap(highlight_status, subset=['Status']) \
                                  .applymap(highlight_label, subset=['Label']) \
                                  .applymap(highlight_eos, subset=['Decoded Text'])
    
    display(styled_last_df)
    
    # Check if sequence ends with EOS
    print("\n🔍 EOS TOKEN ANALYSIS:")
    print("-" * 50)
    
    if total_tokens > 0 and input_ids[-1] == tokenizer.eos_token_id:
        print("✅ Sequence ENDS with EOS token")
        print(f"   Last token: {input_ids[-1]} = '{tokenizer.decode([input_ids[-1]])}'")
        print(f"   Label: {labels[-1]} ({'MASKED' if labels[-1] == -100 else 'TRAINED'})")
    else:
        print("⚠️  Sequence does NOT end with EOS token")
        if total_tokens > 0:
            print(f"   Last token: {input_ids[-1]} = '{tokenizer.decode([input_ids[-1]])}'")
            print(f"   Label: {labels[-1]} ({'MASKED' if labels[-1] == -100 else 'TRAINED'})")
    
    # Check for any EOS tokens in the sequence
    eos_positions = [i for i, token_id in enumerate(input_ids) if token_id == tokenizer.eos_token_id]
    if eos_positions:
        print(f"\n🎯 EOS tokens found at positions: {eos_positions}")
        for pos in eos_positions:
            label_status = 'MASKED' if labels[pos] == -100 else 'TRAINED'
            print(f"   Position {pos}: label = {labels[pos]} ({label_status})")
    else:
        print(f"\n❌ No EOS tokens found in the entire sequence")
    
    return last_df

# Show last 50 tokens analysis
last_tokens_df = show_last_tokens_analysis(sample_data, num_tokens=50)

def analyze_all_samples():
    """Analyze masking patterns across all samples."""
    
    results = []
    
    for i, sample in enumerate(formatted_samples):
        messages = sample["messages"]
        
        # Get instruction tuning data
        input_ids, labels = instruction_pipeline._tokenize_with_masking(messages, tokenizer)
        
        # Calculate statistics
        total_tokens = len(input_ids)
        masked_tokens = sum(1 for l in labels if l == -100)
        trained_tokens = total_tokens - masked_tokens
        training_ratio = trained_tokens / total_tokens * 100
        
        # Get conversation info
        user_content = next((msg['content'] for msg in messages if msg['role'] == 'user'), "")
        assistant_content = next((msg['content'] for msg in messages if msg['role'] == 'assistant'), "")
        
        results.append({
            'Sample': i + 1,
            'Total Tokens': total_tokens,
            'Masked (-100)': masked_tokens,
            'Trained (token_id)': trained_tokens,
            'Training Ratio': f"{training_ratio:.1f}%",
            'User Chars': len(user_content),
            'Assistant Chars': len(assistant_content),
            'Question Preview': user_content[:40] + "..." if len(user_content) > 40 else user_content
        })
    
    df = pd.DataFrame(results)
    
    print("\n📊 INSTRUCTION TUNING ANALYSIS ACROSS ALL SAMPLES")
    print("=" * 70)
    display(df)
    
    # Summary statistics
    avg_training_ratio = sum(float(r['Training Ratio'].rstrip('%')) for r in results) / len(results)
    print(f"\n📈 SUMMARY STATISTICS:")
    print(f"Average training ratio: {avg_training_ratio:.1f}%")
    print(f"Average total tokens: {df['Total Tokens'].mean():.0f}")
    print(f"Average masked tokens: {df['Masked (-100)'].mean():.0f}")
    print(f"Average trained tokens: {df['Trained (token_id)'].mean():.0f}")
    
    return df

# Analyze all samples
analysis_df = analyze_all_samples()