#!/usr/bin/env python3
"""
Test the instruction tuning inspection functionality.
This shows the same analysis as the <PERSON>py<PERSON> notebook but in a simple script.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.fine_tune import FineTuningPipeline
from transformers import AutoTokenizer
import json

def test_instruction_tuning_inspection():
    """Test the instruction tuning inspection functionality."""
    
    print("🔍 INSTRUCTION TUNING DATA INSPECTION")
    print("=" * 60)
    print("This shows exactly what goes into the model during training.")
    print("Focus: System/user prompts masked with -100, assistant responses trained.")
    
    try:
        # Load sample data
        print("\n📁 Loading sample data...")
        data_file = "data/code_switched_GAIR_LIMO_train_817.jsonl"
        samples = []
        with open(data_file, 'r', encoding='utf-8') as f:
            for i, line in enumerate(f):
                if i >= 2:  # Just load first 2 samples
                    break
                samples.append(json.loads(line.strip()))
        
        print(f"Loaded {len(samples)} samples")
        
        # Initialize instruction tuning pipeline
        print("\n⚙️ Setting up instruction tuning pipeline...")
        instruction_pipeline = FineTuningPipeline(config_path="configs/instruction_tuning.yaml")
        print("Instruction tuning enabled:", instruction_pipeline.config["dataset"].get("instruction_tuning", True))
        
        # Load tokenizer
        print("\n🔤 Loading tokenizer...")
        model_name = instruction_pipeline.config["model"]["name"]
        tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)
        special_tokens = ["<think>", "</think>"]
        tokenizer.add_tokens(special_tokens)
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        print(f"Tokenizer loaded: {len(tokenizer)} tokens")
        
        # Format training data
        print("\n📝 Formatting training data...")
        formatted_samples = instruction_pipeline.format_training_data(samples)
        print(f"Formatted {len(formatted_samples)} samples")
        
        # Inspect first sample
        print(f"\n{'='*80}")
        print("SAMPLE 1: INSTRUCTION TUNING INSPECTION")
        print(f"{'='*80}")
        
        sample = formatted_samples[0]
        messages = sample["messages"]
        
        # Show original conversation
        print("\n📋 ORIGINAL CONVERSATION:")
        print("-" * 40)
        for msg in messages:
            print(f"{msg['role'].upper()}: {msg['content'][:150]}{'...' if len(msg['content']) > 150 else ''}")
            print()
        
        # Apply chat template
        full_text = tokenizer.apply_chat_template(
            messages, tokenize=False, add_generation_prompt=False
        )
        print("💬 CHAT TEMPLATE OUTPUT:")
        print("-" * 40)
        print(f"Length: {len(full_text)} characters")
        print(f"Text: {full_text[:200]}{'...' if len(full_text) > 200 else ''}")
        
        # Get instruction tuning tokenization
        input_ids, labels = instruction_pipeline._tokenize_with_masking(messages, tokenizer)
        
        print("\n🎯 INSTRUCTION TUNING TOKENIZATION:")
        print("=" * 50)
        print(f"Input tokens: {len(input_ids)}")
        print(f"Masked tokens (labels = -100): {sum(1 for l in labels if l == -100)}")
        print(f"Trained tokens (labels = token_id): {sum(1 for l in labels if l != -100)}")
        print(f"Training ratio: {sum(1 for l in labels if l != -100) / len(labels) * 100:.1f}%")
        
        # Show detailed token analysis
        print(f"\n🔍 DETAILED TOKEN ANALYSIS (First 30 tokens)")
        print("=" * 90)
        print(f"{'Pos':<4} {'Input ID':<8} {'Label':<8} {'Decoded Text':<25} {'Status':<8} {'Loss':<4}")
        print("-" * 90)
        
        for i in range(min(30, len(input_ids))):
            token_id = input_ids[i]
            label = labels[i]
            token_text = repr(tokenizer.decode([token_id]))
            
            if label == -100:
                status = "MASKED"
                label_display = "-100"
                loss_contrib = "❌"
            else:
                status = "TRAINED"
                label_display = str(label)
                loss_contrib = "✅"
            
            print(f"{i:<4} {token_id:<8} {label_display:<8} {token_text:<25} {status:<8} {loss_contrib:<4}")
        
        if len(input_ids) > 30:
            print(f"... ({len(input_ids) - 30} more tokens)")
        
        # Show text visualization
        print(f"\n🎨 TEXT VISUALIZATION:")
        print("=" * 50)
        print("Legend: [MASKED] = labels=-100, [TRAINED] = labels=token_id")
        print("-" * 50)
        
        line_length = 0
        max_line_length = 70
        
        for i in range(min(50, len(input_ids))):
            token_id = input_ids[i]
            label = labels[i]
            token_text = tokenizer.decode([token_id])
            
            # Format token with status
            if label == -100:
                formatted_token = f"[{token_text}]"  # Masked tokens in brackets
            else:
                formatted_token = token_text  # Trained tokens as-is
            
            # Line wrapping
            if line_length + len(formatted_token) > max_line_length:
                print()
                line_length = 0
            
            print(formatted_token, end='')
            line_length += len(formatted_token)
        
        if len(input_ids) > 50:
            print(f"\n... ({len(input_ids) - 50} more tokens)")
        
        print(f"\n\n📊 SUMMARY:")
        print("=" * 30)
        print("✅ System and user prompts are properly masked with -100")
        print("✅ Assistant responses have actual token IDs as labels")
        print("✅ Only assistant tokens contribute to loss calculation")
        print("✅ This is proper instruction tuning!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_instruction_tuning_inspection()
